@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 网络连通性检测脚本
:: 作者: AI Assistant
:: 功能: 对指定IP地址进行ping测试并显示详细结果

echo ========================================
echo       网络连通性检测工具
echo ========================================
echo.

:: 定义目标IP地址
set "ip1=*******"
set "ip2=***************"
set "ip3=*******"

:: 定义IP描述
set "desc1=Google DNS"
set "desc2=114 DNS"
set "desc3=Cloudflare DNS"

:: 获取当前时间
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (
    set "current_date=%%a/%%b/%%c"
)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (
    set "current_time=%%a:%%b"
)

echo 检测时间: %current_date% %current_time%
echo.

:: 开始检测
echo 开始网络连通性检测...
echo.

:: 检测第一个IP
call :ping_test %ip1% "%desc1%"

:: 检测第二个IP
call :ping_test %ip2% "%desc2%"

:: 检测第三个IP
call :ping_test %ip3% "%desc3%"

echo.
echo ========================================
echo 检测完成！
echo ========================================
pause
goto :eof

:: ping测试函数
:ping_test
set "target_ip=%1"
set "ip_desc=%~2"

echo 正在检测 %target_ip% (%ip_desc%)...
echo ----------------------------------------

:: 执行ping命令并捕获结果
ping -n 4 %target_ip% > temp_ping.txt 2>&1

:: 检查ping命令是否成功执行
if %errorlevel% equ 0 (
    :: 分析ping结果
    call :analyze_ping_result %target_ip% "%ip_desc%"
) else (
    echo [失败] 无法执行ping命令
    echo 状态: 网络不可达
    echo.
)

:: 清理临时文件
if exist temp_ping.txt del temp_ping.txt
goto :eof

:: 分析ping结果函数
:analyze_ping_result
set "target_ip=%1"
set "ip_desc=%~2"
set "success_count=0"
set "total_count=4"
set "avg_time=N/A"
set "loss_rate=0"

:: 检查是否有成功的ping
findstr /c:"TTL=" temp_ping.txt >nul
if %errorlevel% equ 0 (
    :: 有成功的ping，计算统计信息
    for /f "tokens=*" %%i in ('findstr /c:"TTL=" temp_ping.txt') do (
        set /a success_count+=1
    )
    
    :: 提取平均延迟时间
    for /f "tokens=*" %%i in ('findstr /c:"平均 =" temp_ping.txt') do (
        set "avg_line=%%i"
        for /f "tokens=3 delims== " %%j in ("!avg_line!") do (
            set "avg_time=%%j"
        )
    )
    
    :: 如果没找到中文的"平均"，尝试英文的"Average"
    if "!avg_time!"=="N/A" (
        for /f "tokens=*" %%i in ('findstr /c:"Average =" temp_ping.txt') do (
            set "avg_line=%%i"
            for /f "tokens=3 delims== " %%j in ("!avg_line!") do (
                set "avg_time=%%j"
            )
        )
    )
    
    :: 计算丢包率
    set /a loss_count=total_count-success_count
    set /a loss_rate=loss_count*100/total_count
    
    echo [成功] 网络连通正常
    echo 成功包数: !success_count!/!total_count!
    echo 平均延迟: !avg_time!
    echo 丢包率: !loss_rate!%%
    
) else (
    :: 检查是否是超时
    findstr /c:"超时" temp_ping.txt >nul
    if %errorlevel% equ 0 (
        echo [失败] 请求超时
        echo 状态: 目标主机无响应
        echo 丢包率: 100%%
    ) else (
        :: 检查是否是主机不可达
        findstr /c:"无法访问目标主机" temp_ping.txt >nul
        if %errorlevel% equ 0 (
            echo [失败] 无法访问目标主机
            echo 状态: 网络不可达
            echo 丢包率: 100%%
        ) else (
            echo [失败] 未知错误
            echo 状态: 检测失败
            echo 丢包率: 100%%
        )
    )
)

echo.
goto :eof
