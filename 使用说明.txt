网络连通性检测脚本使用说明
=====================================

文件名: network_ping_test.bat

功能描述:
--------
这个批处理脚本可以对指定的3个IP地址进行网络连通性检测，并显示详细的检测结果。

检测的IP地址:
------------
1. ******* (Google DNS)
2. *************** (114 DNS)  
3. ******* (Cloudflare DNS)

功能特性:
--------
✓ 对每个IP发送4个ping数据包进行测试
✓ 显示实时检测结果和时间戳
✓ 记录网络连通性状态（成功/失败）
✓ 显示平均延迟时间（毫秒）
✓ 计算并显示丢包率
✓ 提供友好的错误提示信息
✓ 支持中英文Windows系统

使用方法:
--------
1. 双击运行 network_ping_test.bat 文件
2. 或者在命令提示符中执行: network_ping_test.bat
3. 脚本会自动开始检测，完成后显示结果
4. 按任意键退出程序

输出信息说明:
-----------
- [成功] 网络连通正常: 表示能够成功ping通目标IP
- [失败] 请求超时: 表示网络请求超时，可能是网络延迟过高
- [失败] 无法访问目标主机: 表示网络不可达
- [失败] 未知错误: 表示出现了其他类型的网络问题

结果参数说明:
-----------
- 成功包数: 显示成功接收到回复的数据包数量（格式：成功数/总数）
- 平均延迟: 显示网络延迟的平均值，单位为毫秒(ms)
- 丢包率: 显示数据包丢失的百分比

系统要求:
--------
- Windows 7/8/10/11
- 需要网络连接
- 建议以管理员权限运行以获得最佳效果

故障排除:
--------
1. 如果所有IP都显示失败，请检查：
   - 网络连接是否正常
   - 防火墙设置是否阻止了ping命令
   - 是否连接到互联网

2. 如果部分IP失败：
   - 可能是特定DNS服务器的问题
   - 网络运营商可能阻止了某些IP的访问

3. 如果显示乱码：
   - 脚本已设置UTF-8编码，应该能正常显示中文
   - 如仍有问题，请确保命令提示符支持UTF-8

注意事项:
--------
- 脚本执行一轮检测后会自动停止
- 检测过程中会创建临时文件temp_ping.txt，检测完成后会自动删除
- 建议在网络稳定的环境下运行以获得准确结果

版本信息:
--------
版本: 1.0
创建日期: 2025年8月
兼容性: Windows批处理环境
